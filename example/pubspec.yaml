name: flutter_callkit_incoming_example
description: Demonstrates how to use the flutter_callkit_incoming plugin.

publish_to: "none"

environment:
  sdk: ">=2.18.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_callkit_incoming:
    path: ../

  firebase_core: ^2.12.0
  firebase_messaging: ^14.6.0
  http:
  uuid:
  clipboard: ^0.1.3
  onesignal_flutter:
  agora_rtc_engine:
  agora_uikit:
  permission_handler: ^11.0.1

  #livekit
  livekit_components: ^1.2.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  chat_bubbles: ^1.6.0
  livekit_client: ^2.4.3
  flutter_dotenv: ^5.2.1
  provider: ^6.1.2

dev_dependencies:

flutter:
  uses-material-design: true

  assets:
    - assets/test.png
